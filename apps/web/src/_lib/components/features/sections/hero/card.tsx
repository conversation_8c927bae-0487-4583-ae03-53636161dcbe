import Image from "next/image";
import { env } from "@web/env";
import HeroCircle from "./circle";

export default function HeroCard(): React.ReactElement {
  return (
    <div id="3dcard" className="flex flex-1 justify-center">
      <div id="card-container" className="relative">
        <div
          id="image-container"
          className="bg-light-card dark:bg-dark-card shadow-deep relative h-[26rem] w-72 overflow-hidden rounded-3xl md:h-[29rem] md:w-80"
        >
          <Image
            src={`${env.NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL}/v1751012617/images/persona_keuhv1.webp`}
            alt="image-persona"
            fill
            sizes="100%"
            className="object-cover"
            priority={true}
          />
        </div>
        <HeroCircle />
        <div className="bg-light-purple-700 hover:bg-light-purple-500 text-dark-text outline-light-purple-700 absolute bottom-15 -left-10 cursor-pointer rounded-full px-8 py-2 font-bold outline-2 outline-offset-1 transition-all ease-in active:scale-95 md:-left-15">
          Hire Me
        </div>
      </div>
    </div>
  );
}
